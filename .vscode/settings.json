{
  // Flutter & Dart Settings
  "dart.flutterSdkPath": "",
  "dart.sdkPath": "",
  "dart.checkForSdkUpdates": true,
  "dart.previewFlutterUiGuides": true,
  "dart.previewFlutterUiGuidesCustomTracking": true,
  "dart.flutterOutline": true,
  "dart.flutterCreateAndroidLanguage": "kotlin",
  "dart.flutterCreateIOSLanguage": "swift",
  "dart.flutterCreatePlatforms": [
    "android",
    "ios",
    "web"
  ],
  // Hot Reload Settings
  "dart.flutterHotReloadOnSave": "always",
  "dart.flutterHotRestartOnSave": "never",
  // Code Formatting
  "editor.formatOnSave": true,
  "editor.formatOnType": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": "explicit",
    "source.organizeImports": "explicit"
  },
  "dart.lineLength": 80,
  // Editor Settings
  "editor.rulers": [
    80
  ],
  "editor.wordWrap": "wordWrapColumn",
  "editor.wordWrapColumn": 80,
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  // File Associations
  "files.associations": {
    "*.dart": "dart",
    "pubspec.yaml": "yaml",
    "analysis_options.yaml": "yaml"
  },
  // Search Settings
  "search.exclude": {
    "**/build/**": true,
    "**/.dart_tool/**": true,
    "**/.pub/**": true,
    "**/ios/Pods/**": true,
    "**/android/.gradle/**": true,
    "**/android/build/**": true
  },
  // File Watcher Settings
  "files.watcherExclude": {
    "**/build/**": true,
    "**/.dart_tool/**": true,
    "**/.pub/**": true,
    "**/ios/Pods/**": true,
    "**/android/.gradle/**": true,
    "**/android/build/**": true
  },
  // Terminal Settings
  "terminal.integrated.env.osx": {
    "PATH": "${env:PATH}:${env:HOME}/.pub-cache/bin"
  },
  "terminal.integrated.env.linux": {
    "PATH": "${env:PATH}:${env:HOME}/.pub-cache/bin"
  },
  "terminal.integrated.env.windows": {
    "PATH": "${env:PATH};${env:USERPROFILE}\\AppData\\Local\\Pub\\Cache\\bin"
  },
  // Debugging Settings
  "dart.debugExternalPackageLibraries": false,
  "dart.debugSdkLibraries": false,
  "dart.evaluateGettersInDebugViews": true,
  "dart.evaluateToStringInDebugViews": true,
  // Performance Settings
  "dart.analysisServerFolding": true,
  "dart.closingLabels": true,
  "dart.showTodos": true,
  // Git Settings
  "git.ignoreLimitWarning": true,
  // Emmet Settings
  "emmet.includeLanguages": {
    "dart": "html"
  },
  // Additional Flutter Settings
  "dart.showInspectorNotificationsForWidgetErrors": true,
  "dart.previewCommitCharacters": true,
  "dart.enableCompletionCommitCharacters": true,
  "dart.includeDependenciesInWorkspaceSymbols": true,
  // Bracket Pair Colorizer Settings
  "bracket-pair-colorizer-2.colors": [
    "#ffd700",
    "#da70d6",
    "#87ceeb"
  ],
  // Better Comments Settings
  "better-comments.tags": [
    {
      "tag": "!",
      "color": "#FF2D00",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "?",
      "color": "#3498DB",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "//",
      "color": "#474747",
      "strikethrough": true,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "todo",
      "color": "#FF8C00",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "*",
      "color": "#98C379",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    }
  ]
}