{"Flutter Stateless Widget": {"prefix": "stless", "body": ["class ${1:WidgetName} extends StatelessWidget {", "  const ${1:WidgetName}({Key? key}) : super(key: key);", "", "  @override", "  Widget build(BuildContext context) {", "    return ${2:Container()};", "  }", "}"], "description": "Create a StatelessWidget"}, "Flutter Stateful Widget": {"prefix": "stful", "body": ["class ${1:WidgetName} extends StatefulWidget {", "  const ${1:WidgetName}({Key? key}) : super(key: key);", "", "  @override", "  State<${1:WidgetName}> createState() => _${1:WidgetName}State();", "}", "", "class _${1:WidgetName}State extends State<${1:WidgetName}> {", "  @override", "  Widget build(BuildContext context) {", "    return ${2:Container()};", "  }", "}"], "description": "Create a StatefulWidget"}, "Flutter Scaffold": {"prefix": "scaffold", "body": ["Scaffold(", "  appBar: AppBar(", "    title: Text('${1:Title}'),", "  ),", "  body: ${2:Container()},", ")"], "description": "Create a Scaffold"}, "Flutter Container": {"prefix": "container", "body": ["Container(", "  width: ${1:100},", "  height: ${2:100},", "  decoration: BoxDecoration(", "    color: ${3:Colors.blue},", "    borderRadius: BorderRadius.circular(${4:8}),", "  ),", "  child: ${5:null},", ")"], "description": "Create a Container"}, "Flutter Column": {"prefix": "col", "body": ["Column(", "  mainAxisAlignment: MainAxisAlignment.${1:center},", "  crossAxisAlignment: CrossAxisAlignment.${2:center},", "  children: [", "    ${3:// Add widgets here}", "  ],", ")"], "description": "Create a Column"}, "Flutter Row": {"prefix": "row", "body": ["Row(", "  mainAxisAlignment: MainAxisAlignment.${1:center},", "  crossAxisAlignment: CrossAxisAlignment.${2:center},", "  children: [", "    ${3:// Add widgets here}", "  ],", ")"], "description": "Create a Row"}, "Flutter ListView Builder": {"prefix": "listview", "body": ["ListView.builder(", "  itemCount: ${1:items.length},", "  itemBuilder: (context, index) {", "    return ${2:ListTile(", "      title: Text(items[index]),", "    )};", "  },", ")"], "description": "Create a ListView.builder"}, "Flutter Future Builder": {"prefix": "futurebuilder", "body": ["FutureBuilder<${1:Type}>(", "  future: ${2:futureFunction()},", "  builder: (context, snapshot) {", "    if (snapshot.connectionState == ConnectionState.waiting) {", "      return CircularProgressIndicator();", "    } else if (snapshot.hasError) {", "      return Text('Error: \\${snapshot.error}');", "    } else {", "      return ${3:Text('\\${snapshot.data}')};", "    }", "  },", ")"], "description": "Create a FutureBuilder"}, "Flutter Stream Builder": {"prefix": "streambuilder", "body": ["StreamBuilder<${1:Type}>(", "  stream: ${2:stream},", "  builder: (context, snapshot) {", "    if (snapshot.hasData) {", "      return ${3:Text('\\${snapshot.data}')};", "    } else if (snapshot.hasError) {", "      return Text('Error: \\${snapshot.error}');", "    } else {", "      return CircularProgressIndicator();", "    }", "  },", ")"], "description": "Create a StreamBuilder"}}