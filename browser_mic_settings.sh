#!/bin/bash

# 🌐 فحص وإصلاح إعدادات الميكروفون في المتصفحات
echo "🌐 فحص وإصلاح إعدادات الميكروفون في المتصفحات"
echo "=============================================="
echo ""

# 1. فحص إعدادات Chrome
echo "🔍 فحص إعدادات Google Chrome..."
echo "==============================="

if [ -d "/Applications/Google Chrome.app" ]; then
    echo "✅ Chrome مثبت"
    
    # فحص ملف التفضيلات
    CHROME_PREFS="$HOME/Library/Application Support/Google/Chrome/Default/Preferences"
    if [ -f "$CHROME_PREFS" ]; then
        echo "📁 ملف التفضيلات موجود"
        
        # فحص إعدادات الميكروفون
        if grep -q "audio_capture" "$CHROME_PREFS"; then
            echo "🎤 إعدادات الميكروفون موجودة في Chrome"
        else
            echo "❌ لا توجد إعدادات ميكروفون في Chrome"
        fi
    else
        echo "❌ ملف التفضيلات غير موجود"
    fi
else
    echo "❌ Chrome غير مثبت"
fi

echo ""

# 2. فحص إعدادات Safari
echo "🔍 فحص إعدادات Safari..."
echo "========================"

if [ -d "/Applications/Safari.app" ]; then
    echo "✅ Safari مثبت"
    
    # فحص قاعدة بيانات Safari
    SAFARI_DB="$HOME/Library/Safari/PerSitePreferences.db"
    if [ -f "$SAFARI_DB" ]; then
        echo "📁 قاعدة بيانات Safari موجودة"
    else
        echo "❌ قاعدة بيانات Safari غير موجودة"
    fi
else
    echo "❌ Safari غير مثبت"
fi

echo ""

# 3. إرشادات مفصلة لكل متصفح
echo "📝 إرشادات مفصلة لإصلاح الميكروفون:"
echo "===================================="
echo ""

echo "🔧 لـ Google Chrome:"
echo "   1. اذهب إلى chrome://settings/content/microphone"
echo "   2. تأكد من أن 'Sites can ask to use your microphone' مفعل"
echo "   3. أضف web.whatsapp.com إلى قائمة 'Allow'"
echo "   4. أو انقر على أيقونة القفل بجانب العنوان في WhatsApp Web"
echo ""

echo "🔧 لـ Safari:"
echo "   1. اذهب إلى Safari > Preferences > Websites > Microphone"
echo "   2. اختر 'Allow' لـ web.whatsapp.com"
echo "   3. أو انقر على أيقونة الميكروفون في شريط العنوان"
echo ""

# 4. فتح إعدادات المتصفحات
echo "🚀 فتح إعدادات المتصفحات..."
echo "============================"

# فتح إعدادات Chrome
if [ -d "/Applications/Google Chrome.app" ]; then
    echo "🌐 فتح إعدادات الميكروفون في Chrome..."
    open -a "Google Chrome" "chrome://settings/content/microphone"
    sleep 2
fi

# فتح إعدادات Safari
if [ -d "/Applications/Safari.app" ]; then
    echo "🌐 فتح تفضيلات Safari..."
    osascript << 'EOF'
tell application "Safari"
    activate
    delay 1
    tell application "System Events"
        keystroke "," using command down
        delay 1
        click button "Websites" of toolbar 1 of window 1 of application process "Safari"
        delay 1
    end tell
end tell
EOF
fi

echo ""

# 5. اختبار سريع للميكروفون في المتصفح
echo "🎤 اختبار سريع للميكروفون..."
echo "============================"

# فتح صفحة اختبار الميكروفون
echo "🌐 فتح صفحة اختبار الميكروفون..."
if [ -d "/Applications/Google Chrome.app" ]; then
    open -a "Google Chrome" "https://webcammictest.com/check-mic.html"
else
    open -a "Safari" "https://webcammictest.com/check-mic.html"
fi

echo ""
echo "✅ تم فتح صفحة اختبار الميكروفون"
echo "📝 اختبر الميكروفون في هذه الصفحة أولاً قبل WhatsApp"
echo ""

# 6. نصائح استكشاف الأخطاء
echo "🔧 نصائح استكشاف الأخطاء:"
echo "========================"
echo "1. أعد تشغيل المتصفح تماماً"
echo "2. امسح cache وcookies للمتصفح"
echo "3. تأكد من تحديث المتصفح لآخر إصدار"
echo "4. جرب وضع التصفح الخفي/المجهول"
echo "5. تعطيل الإضافات مؤقتاً"
echo "6. تأكد من عدم وجود تطبيقات أخرى تستخدم الميكروفون"
echo ""

echo "🎉 انتهى فحص إعدادات المتصفحات!"
