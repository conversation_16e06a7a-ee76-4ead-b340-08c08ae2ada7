{"version": "0.2.0", "configurations": [{"name": "Flutter (Debug)", "type": "dart", "request": "launch", "program": "lib/main.dart", "flutterMode": "debug", "args": ["--flavor", "development"]}, {"name": "Flutter (Profile)", "type": "dart", "request": "launch", "program": "lib/main.dart", "flutterMode": "profile", "args": ["--flavor", "development"]}, {"name": "Flutter (Release)", "type": "dart", "request": "launch", "program": "lib/main.dart", "flutterMode": "release", "args": ["--flavor", "production"]}, {"name": "Flutter Web (Debug)", "type": "dart", "request": "launch", "program": "lib/main.dart", "deviceId": "web-server", "flutterMode": "debug", "args": ["--web-port", "3000"]}, {"name": "Flutter Web (Profile)", "type": "dart", "request": "launch", "program": "lib/main.dart", "deviceId": "web-server", "flutterMode": "profile", "args": ["--web-port", "3000"]}, {"name": "Flutter Test", "type": "dart", "request": "launch", "program": "test/", "flutterMode": "debug"}, {"name": "Flutter Integration Test", "type": "dart", "request": "launch", "program": "integration_test/", "flutterMode": "debug"}]}