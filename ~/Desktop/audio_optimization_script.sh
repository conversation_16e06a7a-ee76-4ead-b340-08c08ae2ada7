#!/bin/bash

# 🔊 سكريبت تحسين إعدادات الصوت لنظام macOS
# تم إنشاؤه خصيصاً لتحسين جودة الصوت والأداء

echo "🔊 بدء تحسين إعدادات الصوت..."

# 1. ضبط مستويات الصوت المثلى
echo "📊 ضبط مستويات الصوت..."
osascript -e "set volume output volume 75"
osascript -e "set volume input volume 70" 
osascript -e "set volume alert volume 50"

# 2. تحسين جودة البلوتوث
echo "📶 تحسين جودة البلوتوث..."
defaults write com.apple.BluetoothAudioAgent "Apple Bitpool Min (editable)" -int 40
defaults write com.apple.BluetoothAudioAgent "Apple Bitpool Max (editable)" -int 80

# 3. تحسين buffer size للصوت
echo "⚡ تحسين أداء الصوت..."
defaults write com.apple.audio.AudioMIDISetup "audio device buffer size" -int 512

# 4. تحسين إعدادات النظام الصوتية
echo "🎛️ تحسين إعدادات النظام..."
defaults write com.apple.systemsound "com.apple.sound.beep.volume" -float 0.3
defaults write com.apple.systemsound "com.apple.sound.uiaudio.enabled" -int 1

# 5. تفعيل التبديل التلقائي لأجهزة الصوت
echo "🔄 تفعيل التبديل التلقائي..."
defaults write com.apple.coreaudio "Disable Audio Device Switching" -bool false

# 6. تحسين إعدادات التعرف على الصوت
echo "🎤 تحسين إعدادات الميكروفون..."
defaults write com.apple.speech.recognition.AppleSpeechRecognition.prefs DictationIMIntermediateResults -bool true

echo "✅ تم الانتهاء من تحسين إعدادات الصوت!"
echo "🔄 يُنصح بإعادة تشغيل الجهاز لتطبيق جميع التحسينات."

# عرض الإعدادات الحالية
echo ""
echo "📋 الإعدادات الحالية:"
osascript -e "get volume settings"
