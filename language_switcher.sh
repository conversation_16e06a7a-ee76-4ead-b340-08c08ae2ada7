#!/bin/bash

# 🌐 Language Switcher Script for macOS
# Switch between English and Arabic languages easily

echo "🌐 Language Switcher for macOS"
echo "=============================="
echo ""
echo "Choose your preferred language:"
echo "1. English (US) - الإنجليزية"
echo "2. Arabic (Egypt) - العربية"
echo "3. Show current settings - عرض الإعدادات الحالية"
echo ""
read -p "Enter your choice (1-3): " choice

case $choice in
    1)
        echo "🇺🇸 Setting system language to English..."
        
        # Set English as primary language
        defaults write NSGlobalDomain AppleLanguages -array "en-US" "en" "ar"
        defaults write NSGlobalDomain AppleLocale -string "en_US"
        
        # Set US keyboard layout
        defaults write com.apple.HIToolbox AppleCurrentKeyboardLayoutInputSourceID -string "com.apple.keylayout.US"
        
        # Set US date/time format
        defaults write NSGlobalDomain AppleICUForce24HourTime -bool false
        defaults write NSGlobalDomain AppleMetricUnits -bool false
        
        echo "✅ English language settings applied!"
        echo "🔄 Restarting system services..."
        
        killall Finder
        killall Dock
        killall SystemUIServer
        
        echo "✅ Language changed to English successfully!"
        echo "📝 Note: Some applications may require restart to show changes."
        ;;
        
    2)
        echo "🇪🇬 Setting system language to Arabic..."
        
        # Set Arabic as primary language
        defaults write NSGlobalDomain AppleLanguages -array "ar" "en-US" "en"
        defaults write NSGlobalDomain AppleLocale -string "ar_EG"
        
        # Set Arabic keyboard layout
        defaults write com.apple.HIToolbox AppleCurrentKeyboardLayoutInputSourceID -string "com.apple.keylayout.Arabic"
        
        # Set Arabic date/time format
        defaults write NSGlobalDomain AppleICUForce24HourTime -bool true
        defaults write NSGlobalDomain AppleMetricUnits -bool true
        
        echo "✅ Arabic language settings applied!"
        echo "🔄 Restarting system services..."
        
        killall Finder
        killall Dock
        killall SystemUIServer
        
        echo "✅ تم تغيير اللغة إلى العربية بنجاح!"
        echo "📝 ملاحظة: قد تحتاج بعض التطبيقات إلى إعادة تشغيل لإظهار التغييرات."
        ;;
        
    3)
        echo "📋 Current Language Settings:"
        echo "=============================="
        echo "Languages:"
        defaults read NSGlobalDomain AppleLanguages
        echo ""
        echo "Locale:"
        defaults read NSGlobalDomain AppleLocale
        echo ""
        echo "Current Date/Time:"
        date
        ;;
        
    *)
        echo "❌ Invalid choice. Please run the script again and choose 1, 2, or 3."
        exit 1
        ;;
esac

echo ""
echo "🎉 Language switcher completed!"
