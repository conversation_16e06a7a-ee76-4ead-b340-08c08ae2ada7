#!/bin/bash

# 🎤 اختبار سريع للميكروفون
echo "🎤 اختبار سريع للميكروفون"
echo "=========================="

# فحص الأجهزة المتاحة
echo "📱 الأجهزة المتاحة:"
system_profiler SPAudioDataType | grep -A 5 "Default Input Device: Yes"

echo ""
echo "🔊 مستوى الصوت الحالي:"
osascript -e "get volume settings"

echo ""
echo "🎙️ اختبار الميكروفون باستخدام QuickTime..."

# فتح QuickTime لتسجيل صوتي جديد
osascript << EOF
tell application "QuickTime Player"
    activate
    new audio recording
end tell
EOF

echo "✅ تم فتح QuickTime Player"
echo "📝 يمكنك الآن:"
echo "   1. الضغط على زر التسجيل الأحمر"
echo "   2. التحدث في الميكروفون"
echo "   3. إيقاف التسجيل والاستماع للنتيجة"
echo ""
echo "💡 إذا لم تسمع صوتك، فهناك مشكلة في الميكروفون"
