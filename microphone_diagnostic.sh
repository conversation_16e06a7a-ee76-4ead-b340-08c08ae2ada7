#!/bin/bash

# 🎤 سكريبت فحص وإصلاح مشاكل الميكروفون لنظام macOS
# تم إنشاؤه خصيصاً لحل مشاكل الميكروفون

echo "🎤 فحص وإصلاح مشاكل الميكروفون"
echo "=================================="
echo ""

# 1. فحص أجهزة الصوت المتاحة
echo "📱 فحص أجهزة الصوت المتاحة..."
echo "=============================="
system_profiler SPAudioDataType | grep -E "(Input Device|Input Channels|Manufacturer|Current SampleRate|Transport)"
echo ""

# 2. فحص مستوى الصوت الحالي
echo "🔊 فحص مستويات الصوت الحالية..."
echo "==============================="
osascript -e "get volume settings"
echo ""

# 3. فحص صلاحيات الميكروفون
echo "🔐 فحص صلاحيات الميكروفون..."
echo "============================"
echo "التطبيقات التي لها صلاحية الوصول للميكروفون:"

# فحص قاعدة بيانات الصلاحيات
if [ -f "/Library/Application Support/com.apple.TCC/TCC.db" ]; then
    echo "✅ قاعدة بيانات الصلاحيات موجودة"
else
    echo "❌ قاعدة بيانات الصلاحيات غير موجودة"
fi
echo ""

# 4. اختبار الميكروفون
echo "🎙️ اختبار الميكروفون..."
echo "======================="
echo "سيتم الآن اختبار الميكروفون لمدة 5 ثوان..."
echo "تحدث الآن..."

# تسجيل صوت قصير لاختبار الميكروفون
rec -t wav test_recording.wav trim 0 5 2>/dev/null &
RECORD_PID=$!

# عد تنازلي
for i in {5..1}; do
    echo "⏰ $i ثانية متبقية..."
    sleep 1
done

# إيقاف التسجيل
kill $RECORD_PID 2>/dev/null
wait $RECORD_PID 2>/dev/null

# فحص ملف التسجيل
if [ -f "test_recording.wav" ]; then
    FILE_SIZE=$(stat -f%z test_recording.wav 2>/dev/null || echo "0")
    if [ "$FILE_SIZE" -gt 1000 ]; then
        echo "✅ الميكروفون يعمل بشكل صحيح! (حجم الملف: $FILE_SIZE بايت)"
        echo "🔊 تشغيل التسجيل..."
        afplay test_recording.wav 2>/dev/null
    else
        echo "❌ الميكروفون لا يعمل بشكل صحيح (حجم الملف صغير جداً: $FILE_SIZE بايت)"
    fi
    rm -f test_recording.wav
else
    echo "❌ فشل في إنشاء ملف التسجيل"
fi
echo ""

# 5. إصلاح مشاكل الميكروفون
echo "🔧 إصلاح مشاكل الميكروفون..."
echo "============================"

# رفع مستوى الصوت للميكروفون
echo "📈 رفع مستوى صوت الميكروفون إلى 80%..."
osascript -e "set volume input volume 80"

# إعادة تعيين إعدادات الصوت
echo "🔄 إعادة تعيين إعدادات الصوت..."
sudo killall coreaudiod 2>/dev/null

# انتظار إعادة تشغيل خدمة الصوت
echo "⏳ انتظار إعادة تشغيل خدمة الصوت..."
sleep 3

# تحسين إعدادات الميكروفون
echo "⚙️ تحسين إعدادات الميكروفون..."
defaults write com.apple.speech.recognition.AppleSpeechRecognition.prefs DictationIMIntermediateResults -bool true
defaults write com.apple.speech.recognition.AppleSpeechRecognition.prefs DictationIMLocaleIdentifier -string "ar_SA"

# تفعيل تحسينات الصوت
echo "🎛️ تفعيل تحسينات الصوت..."
defaults write com.apple.coreaudio "Disable Audio Device Switching" -bool false
defaults write com.apple.audio.AudioMIDISetup "audio device buffer size" -int 512

echo ""
echo "✅ تم الانتهاء من إصلاح مشاكل الميكروفون!"
echo ""

# 6. فحص نهائي
echo "🔍 فحص نهائي للإعدادات..."
echo "========================="
echo "مستويات الصوت الجديدة:"
osascript -e "get volume settings"
echo ""

# 7. نصائح إضافية
echo "💡 نصائح إضافية:"
echo "================"
echo "1. تأكد من أن الميكروفون متصل بشكل صحيح"
echo "2. تحقق من إعدادات الخصوصية في تفضيلات النظام"
echo "3. أعد تشغيل التطبيق الذي تريد استخدام الميكروفون معه"
echo "4. إذا استمرت المشكلة، أعد تشغيل الجهاز"
echo ""

echo "🎉 انتهى فحص وإصلاح الميكروفون!"
echo ""
echo "📝 لفتح إعدادات الخصوصية والأمان:"
echo "System Preferences > Security & Privacy > Privacy > Microphone"
