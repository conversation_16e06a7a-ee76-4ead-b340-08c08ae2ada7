[
  // Flutter Specific Shortcuts
  {
    "key": "cmd+shift+r",
    "command": "flutter.hotReload",
    "when": "dart-code:anyProjectLoaded"
  },
  {
    "key": "cmd+shift+s",
    "command": "flutter.hotRestart",
    "when": "dart-code:anyProjectLoaded"
  },
  {
    "key": "cmd+shift+d",
    "command": "flutter.selectDevice",
    "when": "dart-code:anyProjectLoaded"
  },
  {
    "key": "cmd+shift+p",
    "command": "flutter.doctor",
    "when": "dart-code:anyProjectLoaded"
  },
  
  // Code Navigation
  {
    "key": "cmd+shift+o",
    "command": "workbench.action.gotoSymbol"
  },
  {
    "key": "cmd+t",
    "command": "workbench.action.quickOpen"
  },
  
  // Testing Shortcuts
  {
    "key": "cmd+shift+t",
    "command": "flutter.runAllTests",
    "when": "dart-code:anyProjectLoaded"
  },
  {
    "key": "cmd+alt+t",
    "command": "flutter.runTestsInCurrentFile",
    "when": "dart-code:anyProjectLoaded"
  },
  
  // Code Formatting
  {
    "key": "cmd+alt+l",
    "command": "editor.action.formatDocument"
  },
  {
    "key": "cmd+alt+o",
    "command": "editor.action.organizeImports"
  },
  
  // Widget Wrapping (Flutter specific)
  {
    "key": "cmd+shift+w",
    "command": "dart.wrapWithWidget",
    "when": "editorTextFocus && dart-code:anyProjectLoaded"
  },
  {
    "key": "cmd+shift+c",
    "command": "dart.wrapWithColumn",
    "when": "editorTextFocus && dart-code:anyProjectLoaded"
  },
  {
    "key": "cmd+shift+x",
    "command": "dart.wrapWithRow",
    "when": "editorTextFocus && dart-code:anyProjectLoaded"
  },
  
  // Terminal Shortcuts
  {
    "key": "cmd+shift+`",
    "command": "workbench.action.terminal.new"
  },
  
  // Git Shortcuts
  {
    "key": "cmd+shift+g",
    "command": "workbench.view.scm"
  }
]
