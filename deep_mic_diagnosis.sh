#!/bin/bash

# 🔍 فحص شامل لمشكلة الميكروفون مع المتصفحات
echo "🔍 فحص شامل لمشكلة الميكروفون مع المتصفحات"
echo "=============================================="
echo ""

# 1. فحص حالة الميكروفون على مستوى النظام
echo "🎤 فحص حالة الميكروفون على مستوى النظام..."
echo "=========================================="

# فحص أجهزة الصوت
echo "📱 أجهزة الصوت المتاحة:"
system_profiler SPAudioDataType | grep -A 10 "Default Input Device"

echo ""
echo "🔊 مستويات الصوت:"
osascript -e "get volume settings"

echo ""

# 2. فحص العمليات التي تستخدم الميكروفون
echo "🔍 فحص العمليات التي تستخدم الميكروفون..."
echo "========================================"
lsof 2>/dev/null | grep -i "audio\|mic" | head -10

echo ""

# 3. فحص صلاحيات النظام بالتفصيل
echo "🔐 فحص صلاحيات النظام..."
echo "========================"

# فحص قاعدة بيانات TCC
echo "📊 حالة صلاحيات الميكروفون:"
sqlite3 "/Library/Application Support/com.apple.TCC/TCC.db" \
"SELECT client, auth_value, auth_reason, last_modified FROM access WHERE service='kTCCServiceMicrophone';" 2>/dev/null | \
while IFS='|' read -r client auth_value auth_reason last_modified; do
    if [ "$auth_value" = "2" ]; then
        status="✅ مسموح"
    elif [ "$auth_value" = "0" ]; then
        status="❌ مرفوض"
    else
        status="❓ غير محدد"
    fi
    echo "  $client: $status"
done

echo ""

# 4. اختبار الميكروفون مع تطبيقات مختلفة
echo "🧪 اختبار الميكروفون مع تطبيقات مختلفة..."
echo "=========================================="

# اختبار مع QuickTime
echo "🎬 اختبار مع QuickTime Player..."
osascript << 'EOF'
try
    tell application "QuickTime Player"
        activate
        new audio recording
        delay 2
        start (document 1)
        delay 3
        stop (document 1)
        close (document 1) saving no
    end tell
    return "✅ QuickTime يعمل"
on error
    return "❌ QuickTime لا يعمل"
end try
EOF

echo ""

# 5. فحص إعدادات المتصفحات بالتفصيل
echo "🌐 فحص إعدادات المتصفحات بالتفصيل..."
echo "===================================="

# Chrome
if [ -d "/Applications/Google Chrome.app" ]; then
    echo "🔍 فحص Chrome:"
    CHROME_PREFS="$HOME/Library/Application Support/Google/Chrome/Default/Preferences"
    if [ -f "$CHROME_PREFS" ]; then
        # فحص إعدادات الميكروفون في Chrome
        if grep -q "audio_capture" "$CHROME_PREFS"; then
            echo "  📁 إعدادات الميكروفون موجودة"
            # استخراج المواقع المحظورة
            BLOCKED_SITES=$(grep -o '"audio_capture":{[^}]*}' "$CHROME_PREFS" | grep -o '"[^"]*":1' | cut -d'"' -f2)
            if [ ! -z "$BLOCKED_SITES" ]; then
                echo "  ❌ مواقع محظورة:"
                echo "$BLOCKED_SITES" | while read site; do
                    echo "    - $site"
                done
            fi
        else
            echo "  ❓ لا توجد إعدادات ميكروفون"
        fi
    fi
fi

echo ""

# Safari
if [ -d "/Applications/Safari.app" ]; then
    echo "🔍 فحص Safari:"
    # فحص إعدادات Safari
    SAFARI_PLIST="$HOME/Library/Preferences/com.apple.Safari.plist"
    if [ -f "$SAFARI_PLIST" ]; then
        echo "  📁 ملف التفضيلات موجود"
    fi
fi

echo ""

# 6. إصلاح شامل للمشكلة
echo "🔧 إصلاح شامل للمشكلة..."
echo "========================"

# إيقاف جميع المتصفحات
echo "🛑 إيقاف جميع المتصفحات..."
killall "Google Chrome" 2>/dev/null
killall "Safari" 2>/dev/null
killall "Firefox" 2>/dev/null
killall "Microsoft Edge" 2>/dev/null

# إعادة تشغيل خدمة الصوت
echo "🔄 إعادة تشغيل خدمة الصوت..."
sudo killall coreaudiod
sleep 3

# إعادة تعيين جميع صلاحيات الميكروفون
echo "🔐 إعادة تعيين جميع صلاحيات الميكروفون..."
tccutil reset Microphone 2>/dev/null

# رفع مستوى الميكروفون للحد الأقصى
echo "📈 رفع مستوى الميكروفون للحد الأقصى..."
osascript -e "set volume input volume 100"

# تحسين إعدادات الصوت
echo "⚙️ تحسين إعدادات الصوت..."
defaults write com.apple.coreaudio "Disable Audio Device Switching" -bool false
defaults write com.apple.audio.AudioMIDISetup "audio device buffer size" -int 256

echo ""

# 7. اختبار نهائي
echo "🧪 اختبار نهائي..."
echo "================"

echo "🔊 مستويات الصوت الجديدة:"
osascript -e "get volume settings"

echo ""
echo "📱 الجهاز الافتراضي:"
system_profiler SPAudioDataType | grep -A 3 "Default Input Device: Yes"

echo ""

# 8. فتح اختبار شامل
echo "🚀 فتح اختبار شامل..."
echo "===================="

# فتح Chrome مع إعدادات خاصة
echo "🌐 فتح Chrome مع إعدادات محسنة..."
open -a "Google Chrome" --args --use-fake-ui-for-media-stream --enable-logging --v=1

sleep 3

# فتح صفحة اختبار متقدمة
echo "🎤 فتح صفحة اختبار متقدمة..."
open -a "Google Chrome" "https://mictests.com/"

echo ""
echo "✅ تم الانتهاء من الفحص والإصلاح الشامل!"
echo ""

# 9. تعليمات نهائية
echo "📝 تعليمات نهائية:"
echo "=================="
echo "1. في صفحة الاختبار المفتوحة، انقر 'Play' أو 'Test'"
echo "2. عندما يطلب المتصفح صلاحية الميكروفون، انقر 'Allow'"
echo "3. إذا لم يظهر الطلب، انقر على أيقونة الميكروفون في شريط العنوان"
echo "4. إذا فشل الاختبار، أعد تشغيل الجهاز"
echo ""
echo "🎯 إذا نجح الاختبار، جرب WhatsApp Web مرة أخرى"
