#!/bin/bash

# 🗑️ Comprehensive Disk Cleanup Script
# Free up space on macOS by cleaning unnecessary files

echo "🗑️ DISK CLEANUP SCRIPT - HP EliteDesk 800 G2"
echo "============================================="
echo "Current disk usage:"
df -h / | tail -1
echo ""

# Function to show space freed
show_space_freed() {
    local before=$1
    local after=$(df / | tail -1 | awk '{print $3}')
    local freed=$((after - before))
    echo "✅ Space freed: $((freed / 1024))MB"
}

# Get initial disk usage
initial_usage=$(df / | tail -1 | awk '{print $3}')

echo "🎯 PHASE 1: iOS Simulator Cleanup (Expected: ~50GB)"
echo "=================================================="
echo "⚠️  This will remove old iOS simulators and runtime data"
read -p "Continue? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 Cleaning iOS Simulators..."
    
    # Clean iOS Simulator data
    rm -rf ~/Library/Developer/CoreSimulator/Devices/*
    rm -rf ~/Library/Developer/CoreSimulator/Caches/*
    
    # Clean Xcode derived data
    rm -rf ~/Library/Developer/Xcode/DerivedData/*
    
    # Clean iOS device support
    rm -rf ~/Library/Developer/Xcode/iOS\ DeviceSupport/*
    
    echo "✅ iOS Simulator cleanup completed"
    show_space_freed $initial_usage
    initial_usage=$(df / | tail -1 | awk '{print $3}')
fi

echo ""
echo "🎯 PHASE 2: Android SDK Cleanup (Expected: ~15GB)"
echo "=============================================="
echo "⚠️  This will remove old Android SDK versions and AVDs"
read -p "Continue? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 Cleaning Android SDK..."
    
    # Clean Android AVDs
    rm -rf ~/.android/avd/*
    
    # Clean Android SDK build tools (keep latest)
    find ~/Library/Android/sdk/build-tools -maxdepth 1 -type d | sort -V | head -n -2 | xargs rm -rf
    
    # Clean Android system images (keep latest)
    rm -rf ~/Library/Android/sdk/system-images/android-2*
    rm -rf ~/Library/Android/sdk/system-images/android-3[0-2]
    
    echo "✅ Android SDK cleanup completed"
    show_space_freed $initial_usage
    initial_usage=$(df / | tail -1 | awk '{print $3}')
fi

echo ""
echo "🎯 PHASE 3: Downloads & Cache Cleanup (Expected: ~5GB)"
echo "===================================================="
echo "🧹 Cleaning Downloads folder..."

# Clean large downloads
rm -f ~/Downloads/*.dmg
rm -f ~/Downloads/*.zip
rm -f ~/Downloads/*.rar
rm -rf ~/Downloads/untitled*

echo "🧹 Cleaning system caches..."
# Clean user caches
rm -rf ~/Library/Caches/*

# Clean application support caches
find ~/Library/Application\ Support -name "Cache*" -type d -exec rm -rf {} + 2>/dev/null

echo "✅ Downloads & Cache cleanup completed"
show_space_freed $initial_usage
initial_usage=$(df / | tail -1 | awk '{print $3}')

echo ""
echo "🎯 PHASE 4: System Cleanup (Expected: ~2GB)"
echo "=========================================="
echo "🧹 Cleaning system files..."

# Clean Homebrew
brew cleanup --prune=all 2>/dev/null

# Clean trash
rm -rf ~/.Trash/*

# Clean logs
sudo rm -rf /var/log/*.log
sudo rm -rf /var/log/*/*.log

# Clean temporary files
sudo rm -rf /tmp/*
sudo rm -rf /var/tmp/*

echo "✅ System cleanup completed"
show_space_freed $initial_usage

echo ""
echo "📊 FINAL RESULTS:"
echo "================"
df -h / | tail -1
echo ""
echo "🎉 Disk cleanup completed successfully!"
echo "💡 Tip: Run this script monthly to maintain optimal disk space"
