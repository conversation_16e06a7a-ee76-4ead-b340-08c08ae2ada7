#!/bin/bash

# 🎤 إصلاح مشكلة الميكروفون مع WhatsApp Web
echo "🎤 إصلاح مشكلة الميكروفون مع WhatsApp Web"
echo "============================================="
echo ""

# 1. فحص المتصفحات المثبتة
echo "🌐 فحص المتصفحات المثبتة..."
echo "=========================="

BROWSERS=()
if [ -d "/Applications/Google Chrome.app" ]; then
    echo "✅ Google Chrome مثبت"
    BROWSERS+=("Chrome")
fi

if [ -d "/Applications/Safari.app" ]; then
    echo "✅ Safari مثبت"
    BROWSERS+=("Safari")
fi

if [ -d "/Applications/Firefox.app" ]; then
    echo "✅ Firefox مثبت"
    BROWSERS+=("Firefox")
fi

if [ -d "/Applications/Microsoft Edge.app" ]; then
    echo "✅ Microsoft Edge مثبت"
    BROWSERS+=("Edge")
fi

echo ""

# 2. إعطاء صلاحيات الميكروفون للمتصفحات
echo "🔐 إعطاء صلاحيات الميكروفون للمتصفحات..."
echo "========================================="

# إعادة تعيين صلاحيات الميكروفون للمتصفحات
echo "🔄 إعادة تعيين صلاحيات الميكروفون..."

# Chrome
if [[ " ${BROWSERS[@]} " =~ " Chrome " ]]; then
    echo "🔧 إعادة تعيين صلاحيات Chrome..."
    tccutil reset Microphone com.google.Chrome 2>/dev/null
fi

# Safari
if [[ " ${BROWSERS[@]} " =~ " Safari " ]]; then
    echo "🔧 إعادة تعيين صلاحيات Safari..."
    tccutil reset Microphone com.apple.Safari 2>/dev/null
fi

# Firefox
if [[ " ${BROWSERS[@]} " =~ " Firefox " ]]; then
    echo "🔧 إعادة تعيين صلاحيات Firefox..."
    tccutil reset Microphone org.mozilla.firefox 2>/dev/null
fi

# Edge
if [[ " ${BROWSERS[@]} " =~ " Edge " ]]; then
    echo "🔧 إعادة تعيين صلاحيات Edge..."
    tccutil reset Microphone com.microsoft.edgemac 2>/dev/null
fi

echo ""

# 3. فتح إعدادات الخصوصية
echo "⚙️ فتح إعدادات الخصوصية..."
echo "=========================="
open "x-apple.systempreferences:com.apple.preference.security?Privacy_Microphone"

echo "✅ تم فتح إعدادات الخصوصية"
echo ""

# 4. تعليمات مفصلة
echo "📝 تعليمات إصلاح WhatsApp Web:"
echo "=============================="
echo ""
echo "🔒 في إعدادات الخصوصية (التي فتحت الآن):"
echo "   1. انقر على القفل 🔒 في الأسفل وأدخل كلمة المرور"
echo "   2. تأكد من تفعيل ✅ المتصفح الذي تستخدمه:"
echo "      - Google Chrome"
echo "      - Safari"
echo "      - Firefox"
echo "      - Microsoft Edge"
echo ""

echo "🌐 في المتصفح:"
echo "   1. اذهب إلى web.whatsapp.com"
echo "   2. عندما يطلب المتصفح صلاحية الميكروفون، اختر 'Allow' أو 'السماح'"
echo "   3. إذا لم يظهر الطلب، انقر على أيقونة القفل 🔒 بجانب العنوان"
echo "   4. اختر 'Microphone' > 'Allow'"
echo ""

echo "🎙️ في WhatsApp Web:"
echo "   1. اذهب إلى أي محادثة"
echo "   2. انقر مطولاً على أيقونة الميكروفون 🎤"
echo "   3. ابدأ التسجيل"
echo ""

# 5. فتح WhatsApp Web في المتصفح المفضل
echo "🚀 فتح WhatsApp Web..."
echo "===================="

# تحديد المتصفح المفضل
if [[ " ${BROWSERS[@]} " =~ " Chrome " ]]; then
    echo "🌐 فتح WhatsApp Web في Chrome..."
    open -a "Google Chrome" "https://web.whatsapp.com"
elif [[ " ${BROWSERS[@]} " =~ " Safari " ]]; then
    echo "🌐 فتح WhatsApp Web في Safari..."
    open -a "Safari" "https://web.whatsapp.com"
elif [[ " ${BROWSERS[@]} " =~ " Firefox " ]]; then
    echo "🌐 فتح WhatsApp Web في Firefox..."
    open -a "Firefox" "https://web.whatsapp.com"
elif [[ " ${BROWSERS[@]} " =~ " Edge " ]]; then
    echo "🌐 فتح WhatsApp Web في Edge..."
    open -a "Microsoft Edge" "https://web.whatsapp.com"
fi

echo ""
echo "✅ تم فتح WhatsApp Web"
echo ""

# 6. نصائح إضافية
echo "💡 نصائح إضافية:"
echo "================"
echo "1. إذا لم يعمل الميكروفون، أعد تحميل الصفحة (Cmd+R)"
echo "2. تأكد من أن الميكروفون يعمل في تطبيقات أخرى أولاً"
echo "3. جرب متصفح مختلف إذا استمرت المشكلة"
echo "4. تأكد من أن WhatsApp Web محدث"
echo "5. امسح cache المتصفح إذا لزم الأمر"
echo ""

echo "🎉 انتهى إعداد الميكروفون لـ WhatsApp Web!"
echo ""
echo "⚠️ ملاحظة مهمة:"
echo "إذا لم يعمل الميكروفون بعد هذه الخطوات، قم بإعادة تشغيل المتصفح تماماً"
