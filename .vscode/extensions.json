{
  "recommendations": [
    // Essential Flutter & Dart Extensions
    "dart-code.dart-code",
    "dart-code.flutter",
    // Flutter Specialized Extensions
    "alexisvt.flutter-snippets",
    "nash.awesome-flutter-snippets",
    "jeroen-meijer.pubspec-assist",
    "localizely.flutter-intl",
    "ryanluker.vscode-coverage-gutters",
    // Code Quality & Formatting
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "esbenp.prettier-vscode",
    "usernamehw.errorlens",
    "sonarsource.sonarlint-vscode",
    // Git & Version Control
    "eamodio.gitlens",
    "mhutchie.git-graph",
    "donjayamanne.git-extension-pack",
    "github.vscode-pull-request-github",
    // Productivity Extensions
    "formulahendry.auto-rename-tag",
    "formulahendry.auto-close-tag",
    "formulahendry.auto-complete-tag",
    "christian-kohler.path-intellisense",
    "alefragnani.bookmarks",
    "alefragnani.project-manager",
    // Code Enhancement
    "coenraads.bracket-pair-colorizer-2",
    "aaron-bond.better-comments",
    "oderwat.indent-rainbow",
    "ms-vscode.vscode-json",
    // Testing & Debugging
    "ms-vscode.test-adapter-converter",
    "dart-code.dart-code",
    // UI/UX Themes & Icons
    "pkief.material-icon-theme",
    "zhuangtongfa.material-theme",
    "dracula-theme.theme-dracula",
    "johnpapa.vscode-peacock",
    // Documentation & Comments
    "streetsidesoftware.code-spell-checker",
    "gruntfuggly.todo-tree",
    "mintlify.document",
    // API & Database Tools
    "rangav.vscode-thunder-client",
    "humao.rest-client",
    "alexcvzz.vscode-sqlite",
    "ms-vscode.vscode-json",
    // File Management
    "sleistner.vscode-fileutils",
    "patbenatar.advanced-new-file",
    // Additional Helpful Extensions
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "ms-toolsai.jupyter"
  ]
}