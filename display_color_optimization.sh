#!/bin/bash

# 🎨 سكريبت تحسين إعدادات الألوان والوضوح لنظام macOS
# مخصص لشاشة HP E222 وتحسين الجودة البصرية

echo "🎨 بدء تحسين إعدادات الألوان والوضوح..."

# 1. تحسين عمق الألوان إلى أقصى جودة (8-bit)
echo "🌈 تحسين عمق الألوان..."
displayplacer "id:FF9B85E1-F766-090D-EE6B-AE31F6E86C7C res:1920x1080 color_depth:8 enabled:true scaling:off origin:(0,0) degree:0"

# 2. تحسين تنعيم الخطوط والنصوص
echo "📝 تحسين وضوح النصوص..."
defaults write NSGlobalDomain AppleFontSmoothing -int 2
defaults write NSGlobalDomain CGFontRenderingFontSmoothingDisabled -bool false

# 3. تحسين الألوان والتباين
echo "🎯 تحسين التباين والألوان..."
defaults write NSGlobalDomain AppleAquaColorVariant -int 6
defaults write com.apple.universalaccess reduceTransparency -bool false
defaults write com.apple.universalaccess increaseContrast -bool false
defaults write com.apple.universalaccess differentiateWithoutColor -bool false

# 4. تحسين إعدادات الرسوميات
echo "🖼️ تحسين الرسوميات..."
defaults write com.apple.dock no-glass -bool true
defaults write com.apple.windowserver DisplayResolutionEnabled -bool true

# 5. تحسين إعدادات التطبيقات
echo "📱 تحسين إعدادات التطبيقات..."
defaults write NSGlobalDomain NSQuitAlwaysKeepsWindows -bool false
defaults write com.apple.QuickTimePlayerX MGPlayMovieOnOpen -bool true
defaults write com.apple.Safari WebKitDeveloperExtrasEnabledPreferenceKey -bool true

# 6. تحسين إعدادات الألوان للنظام
echo "⚙️ تحسين إعدادات النظام..."
defaults write NSGlobalDomain AppleInterfaceStyle -string "Dark"
defaults write NSGlobalDomain AppleAccentColor -int 1

# 7. إعادة تشغيل الخدمات لتطبيق التغييرات
echo "🔄 تطبيق التغييرات..."
killall Dock
killall SystemUIServer

echo "✅ تم الانتهاء من تحسين إعدادات الألوان والوضوح!"
echo "🎨 الشاشة الآن محسنة للحصول على أفضل جودة بصرية"

# عرض الإعدادات الحالية
echo ""
echo "📋 الإعدادات الحالية:"
displayplacer list | head -15
