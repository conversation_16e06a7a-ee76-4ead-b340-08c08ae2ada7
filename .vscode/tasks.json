{"version": "2.0.0", "tasks": [{"label": "Flutter: Clean", "type": "shell", "command": "flutter", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Get Dependencies", "type": "shell", "command": "flutter", "args": ["pub", "get"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Build APK", "type": "shell", "command": "flutter", "args": ["build", "apk"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Build iOS", "type": "shell", "command": "flutter", "args": ["build", "ios"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Build Web", "type": "shell", "command": "flutter", "args": ["build", "web"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Run Tests", "type": "shell", "command": "flutter", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Ana<PERSON>ze", "type": "shell", "command": "flutter", "args": ["analyze"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Dart: Format", "type": "shell", "command": "dart", "args": ["format", "."], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Doctor", "type": "shell", "command": "flutter", "args": ["doctor", "-v"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}